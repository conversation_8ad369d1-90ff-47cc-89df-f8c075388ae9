<!-- 使用外部样式文件 -->

<div class="card card-front">
    <!-- 语境填空 -->
    {{#Context Cloze}}
    <div class="context-section">
        <div class="section-label chinese-text">📝 语境填空</div>
        <div class="context-cloze english-text" id="contextCloze">
            {{Context Cloze}}
        </div>
        <div class="instruction chinese-text">
            根据上下文，回忆空白处应该填入的单词
        </div>

        <div class="answer-input-section">
            <input type="text" id="userAnswer" class="answer-input english-text" placeholder="请输入您的答案..." autocomplete="off">
            <button id="checkAnswer" class="check-button chinese-text">检查答案</button>
        </div>
    </div>

    <!-- 原始句子 -->
    {{#Original Sentence}}
    <div class="original-sentence-section" id="originalSentenceSection" style="display: none;">
        <div class="section-label chinese-text">📄 原始句子</div>
        <div class="original-sentence english-text">
            {{Original Sentence}}
        </div>
        <div class="instruction chinese-text">
            对比您的答案与原文
        </div>
        <button id="showWord" class="show-word-button chinese-text">显示单词</button>
    </div>
    {{/Original Sentence}}

    <!-- 单词显示 -->
    <div class="word-display-section" id="wordDisplaySection" style="display: none;">
        <div class="section-label chinese-text">💡 答案单词</div>
        <div class="word-display english-text">
            {{Front}}
        </div>
    </div>
    {{/Context Cloze}}

    <!-- 只在没有语境填空时显示单词 -->
    {{^Context Cloze}}
    <div class="word-display english-text">
        {{Front}}
    </div>
    {{/Context Cloze}}
</div>

<script>
// Anki 兼容的简化版本
(function() {
    'use strict';

    // 立即执行函数，避免全局变量冲突
    function initializeCard() {
        // 获取元素
        var contextCloze = document.getElementById('contextCloze');
        var userAnswer = document.getElementById('userAnswer');
        var checkButton = document.getElementById('checkAnswer');
        var originalSection = document.getElementById('originalSentenceSection');
        var showWordBtn = document.getElementById('showWord');
        var wordSection = document.getElementById('wordDisplaySection');

        // 获取数据
        var frontWord = '{{Front}}';
        var contextText = '{{Context Cloze}}';
        var correctAnswer = frontWord;

        // 处理填空
        if (contextCloze && contextText && contextText.indexOf('[') !== -1) {
            // 查找方括号中的内容
            var startIndex = contextText.indexOf('[');
            var endIndex = contextText.indexOf(']');

            if (startIndex !== -1 && endIndex !== -1 && endIndex > startIndex) {
                correctAnswer = contextText.substring(startIndex + 1, endIndex);

                // 替换为空白
                var newText = contextText.replace(/\[([^\]]+)\]/g, '<span class="blank-space">______</span>');
                contextCloze.innerHTML = newText;
            }
        }

        // 按钮事件
        if (checkButton && userAnswer) {
            checkButton.onclick = function() {
                var answer = userAnswer.value.trim();

                if (!answer) {
                    alert('请输入答案！');
                    return;
                }

                if (answer.toLowerCase() === correctAnswer.toLowerCase()) {
                    alert('正确！');
                    checkButton.style.background = '#27ae60';
                    checkButton.textContent = '正确 ✓';
                    checkButton.className = 'check-button correct';

                    if (originalSection) {
                        originalSection.style.display = 'block';
                    }
                } else {
                    alert('错误！正确答案是：' + correctAnswer);
                    checkButton.style.background = '#e74c3c';
                    checkButton.textContent = '错误 ✗';
                    checkButton.className = 'check-button incorrect';
                }
            };

            // 回车键支持
            userAnswer.onkeypress = function(e) {
                if (e.keyCode === 13 || e.which === 13) {
                    checkButton.onclick();
                }
            };
        }

        // 显示单词按钮
        if (showWordBtn && wordSection) {
            showWordBtn.onclick = function() {
                wordSection.style.display = 'block';
                showWordBtn.disabled = true;
                showWordBtn.textContent = '已显示';
            };
        }
    }

    // 确保在 DOM 加载后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeCard);
    } else {
        initializeCard();
    }
})();
</script>
