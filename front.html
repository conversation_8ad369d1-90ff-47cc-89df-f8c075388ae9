<style>
        .card-front {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 10px;
        }
        
        .context-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #4CAF50;
        }
        
        .section-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .context-cloze {
            font-size: 18px;
            line-height: 1.6;
            margin: 15px 0;
            padding: 15px;
            background: #e3f2fd;
            border-radius: 5px;
        }
        
        .blank-space {
            background: #ffeb3b;
            padding: 2px 8px;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .instruction {
            color: #666;
            font-style: italic;
            margin: 10px 0;
        }
        
        .answer-input-section {
            margin: 20px 0;
        }
        
        .answer-input {
            width: 70%;
            padding: 10px;
            font-size: 16px;
            border: 2px solid #ddd;
            border-radius: 5px;
            margin-right: 10px;
        }
        
        .check-button {
            padding: 10px 20px;
            font-size: 16px;
            background: #2196F3;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .check-button:hover {
            background: #1976D2;
        }
        
        .original-sentence-section, .word-display-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border-left: 4px solid #FF9800;
        }
        
        .original-sentence, .word-display {
            font-size: 18px;
            line-height: 1.6;
            margin: 10px 0;
        }
        
        .show-word-button {
            padding: 8px 16px;
            background: #FF9800;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 10px;
        }
    </style>

<div class="card-front">
    <!-- 语境填空 -->
    {{#Context Cloze}}
    <div class="context-section">
        <div class="section-label">📝 语境填空</div>
        <div class="context-cloze" id="contextCloze">
            {{Context Cloze}}
        </div>
        <div class="instruction">
            根据上下文，回忆空白处应该填入的单词
        </div>
        
        <div class="answer-input-section">
            <input type="text" id="userAnswer" class="answer-input" placeholder="请输入您的答案..." autocomplete="off">
            <button id="checkAnswer" class="check-button">检查答案</button>
        </div>
    </div>

    <!-- 原始句子 -->
    {{#Original Sentence}}
    <div class="original-sentence-section" id="originalSentenceSection" style="display: none;">
        <div class="section-label">📄 原始句子</div>
        <div class="original-sentence">
            {{Original Sentence}}
        </div>
        <div class="instruction">
            对比您的答案与原文
        </div>
        <button id="showWord" class="show-word-button">显示单词</button>
    </div>
    {{/Original Sentence}}

    <!-- 单词显示 -->
    <div class="word-display-section" id="wordDisplaySection" style="display: none;">
        <div class="section-label">💡 答案单词</div>
        <div class="word-display">
            {{Front}}
        </div>
    </div>
    {{/Context Cloze}}

    <!-- 只在没有语境填空时显示单词 -->
    {{^Context Cloze}}
    <div class="word-display">
        {{Front}}
    </div>
    {{/Context Cloze}}
</div>

<script>
console.log('Script starting...');

// 等待DOM加载
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded');
    
    // 获取元素
    const contextCloze = document.getElementById('contextCloze');
    const userAnswer = document.getElementById('userAnswer');
    const checkButton = document.getElementById('checkAnswer');
    const originalSection = document.getElementById('originalSentenceSection');
    const showWordBtn = document.getElementById('showWord');
    const wordSection = document.getElementById('wordDisplaySection');
    
    console.log('Elements found:', {
        contextCloze: !!contextCloze,
        userAnswer: !!userAnswer,
        checkButton: !!checkButton
    });
    
    // 获取数据
    const frontWord = '{{Front}}';
    const contextText = '{{Context Cloze}}';
    
    console.log('Data:', { frontWord, contextText });
    
    let correctAnswer = frontWord;
    
    // 处理填空
    if (contextCloze && contextText) {
        console.log('Processing cloze...');
        
        // 查找方括号中的内容
        const match = contextText.match(/\[([^\]]+)\]/);
        if (match) {
            correctAnswer = match[1];
            console.log('Found answer in brackets:', correctAnswer);
            
            // 替换为空白
            const newText = contextText.replace(/\[([^\]]+)\]/g, '<span class="blank-space">______</span>');
            contextCloze.innerHTML = newText;
            console.log('Updated display');
        }
    }
    
    // 按钮事件
    if (checkButton && userAnswer) {
        checkButton.addEventListener('click', function() {
            console.log('Button clicked');
            
            const answer = userAnswer.value.trim();
            console.log('User answer:', answer, 'Correct:', correctAnswer);
            
            if (!answer) {
                alert('请输入答案！');
                return;
            }
            
            if (answer.toLowerCase() === correctAnswer.toLowerCase()) {
                alert('正确！');
                checkButton.style.background = 'green';
                checkButton.textContent = '正确 ✓';
                
                if (originalSection) {
                    originalSection.style.display = 'block';
                }
            } else {
                alert('错误！正确答案是：' + correctAnswer);
                checkButton.style.background = 'red';
                checkButton.textContent = '错误 ✗';
            }
        });
    }
    
    // 显示单词按钮
    if (showWordBtn && wordSection) {
        showWordBtn.addEventListener('click', function() {
            wordSection.style.display = 'block';
        });
    }
    
    console.log('Initialization complete');
});
</script>
