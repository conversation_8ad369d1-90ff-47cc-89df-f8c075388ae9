<div class="card-front">
    <!-- 语境填空 - 作为主要学习内容 -->
    {{#Context Cloze}}
    <div class="context-section">
        <div class="section-label">📝 语境填空</div>
        <div class="context-cloze" id="contextCloze">
            {{Context Cloze}}
        </div>
        <div class="instruction">
            根据上下文，回忆空白处应该填入的单词
        </div>

        <!-- 答案输入区域 -->
        <div class="answer-input-section">
            <input type="text" id="userAnswer" class="answer-input" placeholder="请输入您的答案..." autocomplete="off">
            <button id="checkAnswer" class="check-button">检查答案</button>
        </div>
    </div>

    <!-- 原始句子 - 初始隐藏，输入答案后显示 -->
    {{#Original Sentence}}
    <div class="original-sentence-section" id="originalSentenceSection" style="display: none;">
        <div class="section-label">📄 原始句子</div>
        <div class="original-sentence">
            {{Original Sentence}}
        </div>
        <div class="instruction">
            对比您的答案与原文
        </div>
        <button id="showWord" class="show-word-button">显示单词</button>
    </div>
    {{/Original Sentence}}

    <!-- 单词显示 - 最后显示 -->
    <div class="word-display-section" id="wordDisplaySection" style="display: none;">
        <div class="section-label">💡 答案单词</div>
        <div class="word-display">
            {{Front}}
        </div>
    </div>
    {{/Context Cloze}}

    <!-- 只在没有语境填空时显示单词 -->
    {{^Context Cloze}}
    <div class="word-display">
        {{Front}}
    </div>
    {{/Context Cloze}}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const userAnswerInput = document.getElementById('userAnswer');
    const checkAnswerBtn = document.getElementById('checkAnswer');
    const originalSentenceSection = document.getElementById('originalSentenceSection');
    const showWordBtn = document.getElementById('showWord');
    const wordDisplaySection = document.getElementById('wordDisplaySection');
    const contextClozeElement = document.getElementById('contextCloze');

    // 获取正确答案（从Front字段）
    const correctAnswer = '{{Front}}';
    let attemptCount = 0;
    const maxAttempts = 3;

    // 处理语境填空格式
    function processClozeText() {
        if (!contextClozeElement) return;

        let clozeText = contextClozeElement.innerHTML;

        // 处理不同的填空格式
        // 格式1: [word] -> ______
        clozeText = clozeText.replace(/\[([^\]]+)\]/g, '<span class="blank-space">______</span>');

        // 格式2: {{c1::word}} -> ______
        clozeText = clozeText.replace(/\{\{c\d+::([^}]+)\}\}/g, '<span class="blank-space">______</span>');

        // 格式3: 多个下划线 -> 标准化空白
        clozeText = clozeText.replace(/_{3,}/g, '<span class="blank-space">______</span>');

        contextClozeElement.innerHTML = clozeText;
    }

    // 提取正确答案（如果在Context Cloze中有标记）
    function extractAnswerFromCloze() {
        const contextClozeText = '{{Context Cloze}}';
        let extractedAnswer = correctAnswer;

        // 从[word]格式提取
        const bracketMatch = contextClozeText.match(/\[([^\]]+)\]/);
        if (bracketMatch) {
            extractedAnswer = bracketMatch[1];
        }

        // 从{{c1::word}}格式提取
        const clozeMatch = contextClozeText.match(/\{\{c\d+::([^}]+)\}\}/);
        if (clozeMatch) {
            extractedAnswer = clozeMatch[1];
        }

        return extractedAnswer.trim();
    }

    // 初始化
    processClozeText();
    const finalCorrectAnswer = extractAnswerFromCloze();

    // 创建反馈区域
    function createFeedbackArea() {
        const feedbackDiv = document.createElement('div');
        feedbackDiv.id = 'feedbackArea';
        feedbackDiv.className = 'feedback-area';
        userAnswerInput.parentNode.insertBefore(feedbackDiv, userAnswerInput.nextSibling);
        return feedbackDiv;
    }

    const feedbackArea = createFeedbackArea();

    // 显示反馈信息
    function showFeedback(message, type) {
        feedbackArea.innerHTML = message;
        feedbackArea.className = `feedback-area feedback-${type}`;
        feedbackArea.style.display = 'block';
    }

    // 隐藏反馈信息
    function hideFeedback() {
        feedbackArea.style.display = 'none';
    }

    // 答案验证函数
    function validateAnswer(userAnswer, correctAnswer) {
        // 标准化答案（去除空格，转换为小写）
        const normalizedUser = userAnswer.toLowerCase().trim();
        const normalizedCorrect = correctAnswer.toLowerCase().trim();

        // 完全匹配
        if (normalizedUser === normalizedCorrect) {
            return { isCorrect: true, similarity: 1 };
        }

        // 检查是否包含正确答案（处理复合词情况）
        if (normalizedUser.includes(normalizedCorrect) || normalizedCorrect.includes(normalizedUser)) {
            return { isCorrect: true, similarity: 0.8 };
        }

        // 计算相似度（简单的编辑距离）
        const similarity = calculateSimilarity(normalizedUser, normalizedCorrect);
        return { isCorrect: similarity > 0.7, similarity: similarity };
    }

    // 计算字符串相似度
    function calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;

        if (longer.length === 0) return 1.0;

        const editDistance = levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }

    // 计算编辑距离
    function levenshteinDistance(str1, str2) {
        const matrix = [];
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        return matrix[str2.length][str1.length];
    }

    // 检查答案按钮点击事件
    if (checkAnswerBtn) {
        checkAnswerBtn.addEventListener('click', function() {
            const userAnswer = userAnswerInput.value.trim();
            if (!userAnswer) {
                showFeedback('⚠️ 请先输入您的答案', 'warning');
                return;
            }

            attemptCount++;
            const validation = validateAnswer(userAnswer, finalCorrectAnswer);

            if (validation.isCorrect) {
                // 答案正确
                showFeedback('🎉 答案正确！', 'success');
                setTimeout(() => {
                    // 显示原始句子部分
                    if (originalSentenceSection) {
                        originalSentenceSection.style.display = 'block';
                        originalSentenceSection.scrollIntoView({ behavior: 'smooth' });
                    }
                    // 禁用输入框和按钮
                    userAnswerInput.disabled = true;
                    checkAnswerBtn.disabled = true;
                    checkAnswerBtn.textContent = '✓ 正确';
                    checkAnswerBtn.className = 'check-button correct';
                }, 1000);
            } else {
                // 答案错误
                if (attemptCount >= maxAttempts) {
                    // 达到最大尝试次数
                    showFeedback(`❌ 答案错误。正确答案是：<strong>${finalCorrectAnswer}</strong>`, 'error');
                    setTimeout(() => {
                        if (originalSentenceSection) {
                            originalSentenceSection.style.display = 'block';
                            originalSentenceSection.scrollIntoView({ behavior: 'smooth' });
                        }
                        userAnswerInput.disabled = true;
                        checkAnswerBtn.disabled = true;
                        checkAnswerBtn.textContent = '✗ 错误';
                        checkAnswerBtn.className = 'check-button incorrect';
                    }, 2000);
                } else {
                    // 还有尝试机会
                    const remainingAttempts = maxAttempts - attemptCount;
                    let hint = '';

                    if (validation.similarity > 0.5) {
                        hint = ' 很接近了！';
                    } else if (finalCorrectAnswer.length > 0) {
                        hint = ` 提示：单词长度为 ${finalCorrectAnswer.length} 个字母`;
                    }

                    showFeedback(`❌ 答案错误${hint}，还有 ${remainingAttempts} 次机会`, 'error');

                    // 清空输入框，重新聚焦
                    setTimeout(() => {
                        userAnswerInput.value = '';
                        userAnswerInput.focus();
                        hideFeedback();
                    }, 2000);
                }
            }
        });
    }

    // 显示单词按钮点击事件
    if (showWordBtn) {
        showWordBtn.addEventListener('click', function() {
            if (wordDisplaySection) {
                wordDisplaySection.style.display = 'block';
                wordDisplaySection.scrollIntoView({ behavior: 'smooth' });
            }
            showWordBtn.disabled = true;
            showWordBtn.textContent = '已显示';
        });
    }

    // 回车键提交答案
    if (userAnswerInput) {
        userAnswerInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !checkAnswerBtn.disabled) {
                checkAnswerBtn.click();
            }
        });

        // 输入时隐藏反馈
        userAnswerInput.addEventListener('input', function() {
            hideFeedback();
        });
    }
});
</script>
