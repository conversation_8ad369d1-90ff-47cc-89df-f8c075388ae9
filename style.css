/* 基础卡片样式 */
.card {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    max-width: 650px;
    margin: 0 auto;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    line-height: 1.6;
}

/* 正面样式优化 */
.card-front {
    text-align: center;
}

.word-display {
    font-size: 2.8em;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    letter-spacing: 1px;
}

/* 正面各部分通用样式 */
.original-sentence-section,
.context-section,
.explanation-section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-top: 20px;
}

.section-label {
    font-size: 1.1em;
    font-weight: 600;
    color: #34495e;
    margin-bottom: 15px;
    text-align: left;
}

/* 语境填空特殊样式 */
.context-cloze {
    font-size: 1.4em;
    line-height: 1.9;
    color: #2c3e50;
    background: linear-gradient(135deg, #e3f2fd 0%, #f8f9fa 100%);
    padding: 25px;
    border-radius: 10px;
    border-left: 5px solid #2196f3;
    margin: 15px 0;
    text-align: left;
    position: relative;
}

.context-cloze::before {
    content: "💭";
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.2em;
    opacity: 0.6;
}

/* 原始句子样式 */
.original-sentence {
    font-size: 1.2em;
    line-height: 1.8;
    color: #2c3e50;
    background: #f0f8ff;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #9b59b6;
    margin: 15px 0;
    text-align: left;
}

/* 解释说明样式 */
.explanation {
    font-size: 1.1em;
    line-height: 1.7;
    color: #2c3e50;
    background: #fff8dc;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #e67e22;
    margin: 15px 0;
    text-align: left;
}

.instruction {
    font-size: 0.9em;
    color: #7f8c8d;
    font-style: italic;
    margin-top: 15px;
    text-align: center;
}

/* 答案输入区域样式 */
.answer-input-section {
    margin-top: 25px;
    text-align: center;
}

.answer-input {
    width: 100%;
    max-width: 400px;
    padding: 15px 20px;
    font-size: 1.2em;
    border: 2px solid #bdc3c7;
    border-radius: 25px;
    outline: none;
    transition: all 0.3s ease;
    background: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 15px;
}

.answer-input:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
    transform: translateY(-2px);
}

.answer-input:disabled {
    background-color: #ecf0f1;
    color: #7f8c8d;
    cursor: not-allowed;
}

.check-button, .show-word-button {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    font-size: 1.1em;
    font-weight: 600;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
    margin: 0 10px;
}

.check-button:hover, .show-word-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
}

.check-button:disabled, .show-word-button:disabled {
    background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.show-word-button {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.show-word-button:hover {
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

/* 单词显示区域样式 */
.word-display-section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-top: 20px;
    text-align: center;
    border: 3px solid #27ae60;
    animation: slideInUp 0.5s ease;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 分割线 */
.divider {
    border: none;
    height: 2px;
    background: linear-gradient(to right, transparent, #bdc3c7, transparent);
    margin: 30px 0;
}

/* 背面样式 */
.card-back {
    margin-top: 20px;
}

.info-section {
    background: white;
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    transition: transform 0.2s ease;
}

/* 信息区块悬停效果增强 */
.info-section:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 18px 25px;
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 1.05em;
}

.section-header .icon {
    font-size: 1.2em;
    margin-right: 10px;
}

.section-header .title {
    font-size: 1.1em;
}

/* 发音样式增强 */
.pronunciation {
    padding: 25px;
    font-size: 1.6em;
    font-weight: 500;
    color: #1565c0;
    text-align: center;
    background: linear-gradient(135deg, #f3e5f5 0%, #f8f9fa 100%);
    font-family: 'Courier New', monospace;
    letter-spacing: 2px;
}

/* 核心释义样式 */
.meanings {
    background: linear-gradient(135deg, #fff3e0 0%, #f8f9fa 100%);
    border-radius: 10px;
    padding: 25px;
    line-height: 1.8;
    color: #2c3e50;
    font-size: 1.1em;
}

/* 词族样式优化 */
.word-family {
    padding: 25px;
    color: #2c3e50;
    line-height: 1.8;
    background: linear-gradient(135deg, #e8f5e8 0%, #f8f9fa 100%);
    font-size: 1.05em;
}

/* 词源助记样式 */
.etymology {
    background: linear-gradient(135deg, #fff8e1 0%, #f8f9fa 100%);
    padding: 25px;
    border-radius: 8px;
    position: relative;
}

.etymology::before {
    content: "🧠";
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 1.3em;
    opacity: 0.7;
}

/* 近反义词样式 */
.synonyms {
    background: linear-gradient(135deg, #fce4ec 0%, #f8f9fa 100%);
    padding: 25px;
    border-radius: 8px;
}

/* 例句搭配样式 */
.examples {
    background: linear-gradient(135deg, #e0f2f1 0%, #f8f9fa 100%);
    padding: 25px;
    border-radius: 8px;
    position: relative;
}

.examples::before {
    content: "💡";
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 1.3em;
    opacity: 0.7;
}

/* 移动端适配增强 */
@media (max-width: 480px) {
    .card {
        padding: 15px;
        margin: 10px;
        max-width: 95%;
    }

    .word-display {
        font-size: 2.2em;
        margin-bottom: 20px;
    }

    .context-cloze {
        font-size: 1.2em;
        padding: 20px;
    }

    .pronunciation {
        font-size: 1.3em;
        padding: 20px;
    }

    .section-header {
        padding: 15px 20px;
        font-size: 1em;
    }

    .answer-input {
        font-size: 1.1em;
        padding: 12px 18px;
    }

    .check-button, .show-word-button {
        padding: 10px 25px;
        font-size: 1em;
        margin: 5px;
        display: block;
        width: 100%;
        max-width: 200px;
        margin: 10px auto;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .card {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: #ecf0f1;
    }

    .word-display {
        color: #ecf0f1;
    }

    .original-sentence-section,
    .context-section,
    .explanation-section,
    .info-section,
    .word-display-section {
        background: #34495e;
        color: #ecf0f1;
    }

    .answer-input {
        background: #2c3e50;
        color: #ecf0f1;
        border-color: #7f8c8d;
    }

    .answer-input:focus {
        border-color: #3498db;
        background: #34495e;
    }

    .answer-input:disabled {
        background: #2c3e50;
        color: #95a5a6;
    }
}

/* 打印样式 */
@media print {
    .card {
        box-shadow: none;
        background: white;
    }
    
    .info-section {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
}
